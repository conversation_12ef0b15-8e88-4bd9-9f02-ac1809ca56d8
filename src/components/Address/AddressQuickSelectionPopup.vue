<template>
  <van-popup v-model:show="visible" round position="bottom" :style="{ height: '80%' }" @close="handleClose">
    <div class="address-quick-selection-popup">
      <!-- 弹窗头部 -->
      <div class="popup-header">
        <h3 class="title">选择地址</h3>
        <div class="close-btn" @click="handleClose">
          <img src="@/static/images/close.png" alt="关闭" loading="lazy" />
        </div>
      </div>

      <!-- 提示信息 - 可配置显示 -->
      <div class="address-tips" v-if="showTips && tipsText">
        <span class="tips-text" v-html="tipsText"></span>
      </div>

      <!-- 地址列表 -->
      <div class="address-list" ref="addressListRef" v-if="hasAddressList">
        <div v-for="address in actualAddressList" :key="address.addressId"
          :ref="el => setAddressItemRef(el, address.addressId)" :data-address-id="address.addressId">
          <AddressItem :address="address" @click="handleSelectAddress" @edit="handleEditAddress"
            @delete="handleDeleteAddress" />
        </div>
      </div>

      <!-- 空状态 - 优化图片加载 -->
      <div class="empty-state" v-else>
        <img src="./assets/no-address.png" alt="暂无地址" class="empty-image" loading="lazy" decoding="async" />
      </div>

      <!-- 底部操作栏 -->
      <WoActionBar class="action-bar">
        <WoButton type="primary" block size="xlarge" @click="handleCreateNewAddress">
          新建收货地址
        </WoButton>
      </WoActionBar>
    </div>
  </van-popup>
</template>

<script setup>
import { defineProps, defineEmits, toRefs, ref, watch, nextTick, computed, shallowRef, onUnmounted, readonly } from 'vue'
import { showToast, showLoadingToast, closeToast } from 'vant'
import { useUserStore } from '@/store/modules/user'
import { updateUserDefaultAddr } from '@/api/index.js'
import WoButton from '@/components/WoElementCom/WoButton/WoButton.vue'
import WoActionBar from '@/components/WoElementCom/WoActionBar.vue'
import AddressItem from '@/components/Address/AddressItem.vue'
import { debounce } from 'lodash-es'

// ==================== Props 定义 ====================
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  addressList: {
    type: Array,
    default: null
  },
  selectedAddressId: {
    type: [String, Number],
    default: null
  },
  showTips: {
    type: Boolean,
    default: false
  },
  tipsText: {
    type: String,
    default: '23:10前付款，预计明天（03月04日）到达'
  }
})

// ==================== Emits 定义 ====================
const emit = defineEmits([
  'close',
  'select',
  'create',
  'edit',
  'delete',
  'update:visible'
])

// ==================== 响应式数据 ====================
const { visible } = toRefs(props)
const userStore = useUserStore()

// 使用 shallowRef 优化大数组性能
const internalAddressList = shallowRef([])
const internalSelectedAddressId = ref(null)
const loading = ref(false)

// DOM 引用
const addressListRef = ref(null)
const addressItemRefs = shallowRef(new Map())

// ==================== 计算属性 ====================
// 实际使用的地址列表
const actualAddressList = computed(() => {
  return props.addressList !== null ? props.addressList : internalAddressList.value
})

// 实际使用的选中地址ID
const actualSelectedAddressId = computed(() => {
  return props.selectedAddressId !== null ? props.selectedAddressId : internalSelectedAddressId.value
})

// 是否有地址列表（优化渲染判断）
const hasAddressList = computed(() => {
  return actualAddressList.value.length > 0
})

// ==================== 核心业务逻辑 ====================
// 从store获取地址列表
const loadAddressListFromStore = async () => {
  try {
    // 先检查登录状态
    await userStore.queryLoginStatus()
    console.warn(2131323);

    // 如果已登录，查询默认地址和地址列表
    if (userStore.isLogin) {
      console.warn(778978779);
      await Promise.all([
        userStore.queryDefaultAddr({force: true}),
        userStore.queryAddrList({force: true})
      ])
    }

    internalAddressList.value = userStore.addressList || []

    // 计算默认选中的地址ID
    const defaultAddress = internalAddressList.value.find(item => item.isDefault === '1')
    if (defaultAddress) {
      internalSelectedAddressId.value = defaultAddress.addressId
    }
  } catch (error) {
    console.error('加载地址列表失败:', error)
    internalAddressList.value = []
  }
}

// ==================== DOM 操作相关 ====================
// 设置地址项ref（优化内存使用）
const setAddressItemRef = (el, addressId) => {
  if (el) {
    addressItemRefs.value.set(addressId, el)
  } else {
    addressItemRefs.value.delete(addressId)
  }
}

// 滚动到指定地址（防抖优化）
// 创建防抖函数（在组件外部定义避免重复创建）
const scrollToAddress = debounce((addressId) => {
  if (addressListRef.value && addressId) {
    const selectedElement = addressItemRefs.value.get(addressId);
    if (selectedElement) {
      selectedElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
    }
  }
}, 100);

// ==================== 事件处理器 ====================
// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}

// 选择地址（设置默认地址）
const handleSelectAddress = async (address) => {
  if (loading.value) return // 防止重复操作

  showLoadingToast()

  try {
    const [err] = await updateUserDefaultAddr(address.addressId)

    if (!err) {
      // 更新本地状态
      const currentList = actualAddressList.value
      currentList.forEach(item => {
        item.isDefault = item.addressId === address.addressId ? '1' : '0'
      })

      // 如果使用内部地址列表，更新它
      if (props.addressList === null) {
        internalAddressList.value = [...currentList]
      }

      // 更新store中的地址列表数据
      userStore.setAddrList(currentList)

      // 更新store中的当前默认地址数据
      userStore.setDefaultAddr(address)

      showToast('设置默认地址成功')

      // 发送选择事件并关闭弹窗
      emit('select', address)
      handleClose()
    } else {
      console.error('设置默认地址失败:', err)
      showToast(err.msg || '设置默认地址失败')
    }
  } catch (error) {
    console.error('设置默认地址异常:', error)
    showToast('设置默认地址失败')
  } finally {
    closeToast()
  }
}

// 新建地址
const handleCreateNewAddress = () => {
  emit('create')
}

// 编辑地址
const handleEditAddress = (address) => {
  emit('edit', address)
}

// 删除地址
const handleDeleteAddress = (address) => {
  emit('delete', address)
}

// ==================== 监听器 ====================
// 监听弹窗显示状态（优化加载时机和默认地址选中）
watch(visible, async (newVisible) => {
  if (newVisible) {
    // 每次弹窗展示时都要加载最新的地址数据
    await loadAddressListFromStore()

    // 每次弹窗展开时都要重新确定选中的地址ID并滚动定位
    nextTick(() => {
      let targetAddressId = null

      // 1. 如果指定了selectedAddressId，以这个为准
      if (props.selectedAddressId !== null) {
        targetAddressId = props.selectedAddressId
      } else {
        // 2. 如果没有指定，查找列表中isDefault为'1'的地址
        const currentList = actualAddressList.value
        const defaultAddress = currentList.find(item => item.isDefault === '1')
        if (defaultAddress) {
          targetAddressId = defaultAddress.addressId
          // 更新内部选中状态
          if (props.addressList === null) {
            internalSelectedAddressId.value = defaultAddress.addressId
          }
        }
      }

      // 3. 滚动到目标地址
      if (targetAddressId) {
        scrollToAddress(targetAddressId)
      }
    })
  }
}, { immediate: false })


// 组件卸载时取消防抖
onUnmounted(() => {
  scrollToAddress.cancel();
});

// ==================== 组件暴露 ====================
// 暴露方法供外部调用
defineExpose({
  scrollToAddress,
  loadAddressList: loadAddressListFromStore,
  addressList: readonly(internalAddressList)
})
</script>

<style scoped lang="less">
.address-quick-selection-popup {
  background: #F8F9FA;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-bottom: 65px;
  box-sizing: border-box;

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 16px 21px;
    flex-shrink: 0;

    .title {
      font-size: @font-size-17;
      font-weight: @font-weight-600;
      color: @text-color-primary;
      margin: 0;
      text-align: center;
    }

    .close-btn {
      position: absolute;
      right: 20px;
      width: 24px;
      height: 24px;
      border: none;
      background: none;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;

      img {
        width: 24px;
        height: 24px;
      }

      &:hover {
        opacity: 0.7;
      }
    }
  }

  .address-tips {
    margin: 0 10px 10px 10px;

    .tips-text {
      color: @text-color-tips;
      font-size: @font-size-15;
      line-height: 1.5;
    }
  }

  .address-list {
    flex: 1;
    overflow-y: auto;
    padding: 0 10px;
    will-change: scroll-position;
    contain: layout style paint;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #F1F1F1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #C1C1C1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #A8A8A8;
    }
  }

  .empty-state {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    // 优化渲染性能
    contain: layout style;

    .empty-image {
      width: 200px;
      height: 200px;
      // 优化图片渲染
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
    }

    .empty-text {
      font-size: @font-size-16;
      color: @text-color-secondary;
      margin: 0;
    }
  }
}
</style>
