<template>
  <div class="address-page" :class="{ 'address-page--has-data': addressList.length > 0 }">
    <div class="address-page__container">
      <!-- 骨架屏 -->
      <div class="address-page__list" v-if="loading">
        <div v-for="index in 3" :key="`skeleton-${index}`" class="address-page__item">
          <div class="address-skeleton">
            <div class="address-skeleton__content">
              <div class="address-skeleton__header">
                <div class="address-skeleton__user-info">
                  <div class="skeleton-line skeleton-name"></div>
                  <div class="skeleton-line skeleton-phone"></div>
                </div>
              </div>
              <div class="skeleton-line skeleton-address"></div>
              <div class="skeleton-line skeleton-address-detail"></div>
            </div>
            <div class="address-skeleton__actions">
              <div class="skeleton-line skeleton-btn"></div>
              <div class="skeleton-line skeleton-btn"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 地址列表 -->
      <div class="address-page__list" ref="addressListRef" v-show="!loading && addressList.length > 0">
        <div v-for="address in addressList" :key="address.addressId"
          :ref="el => setAddressItemRef(el, address.addressId)" :data-address-id="address.addressId"
          class="address-page__item">
          <AddressItem :address="address" @click="handleSelectAddress" @edit="handleEditAddress"
            @delete="handleDeleteAddress" />
        </div>
      </div>

      <!-- 空状态 -->
      <WoEmpty v-if="!loading && addressList.length === 0" @add="handleAddAddress"
        image="@/static/images/empty-address.png" description="暂无收货地址" class="address-page__empty" />
    </div>

    <!-- 操作栏 -->
    <AddressActionBar size="xlarge" @add="handleAddAddress" class="address-page__actions" />
  </div>
</template>

<script setup>
// ==================== 导入模块 ====================
import { ref, onMounted, onUnmounted, nextTick, shallowRef, readonly } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showLoadingToast, closeToast } from 'vant'
import { debounce } from 'lodash-es'
import { useUserStore } from '@store/modules/user.js'
import { updateUserDefaultAddr, deleteAddr } from '@api/index.js'
import AddressItem from '@components/Address/AddressItem.vue'
import WoEmpty from '@components/WoElementCom/WoEmpty.vue'
import AddressActionBar from '@components/WoElementCom/WoActionBar.vue'
import { useAlert } from '@/hooks/index.js'

// ==================== 路由和Store ====================
const router = useRouter()
const userStore = useUserStore()
const $alert = useAlert()

// ==================== Props和Emits ====================
// ==================== 响应式数据 ====================
// 使用 shallowRef 优化大数组性能
const addressList = shallowRef([])
const loading = ref(false)
const error = ref(null)

// DOM引用
const addressListRef = ref(null)
const addressItemRefs = ref(new Map())

// ==================== 业务逻辑方法 ====================
// 设置地址项ref（优化内存管理）
const setAddressItemRef = (el, addressId) => {
  if (el) {
    addressItemRefs.value.set(addressId, el)
  } else {
    addressItemRefs.value.delete(addressId)
  }
}

// 从store加载地址列表
const loadAddressList = async () => {
  if (loading.value) return // 防止重复加载

  loading.value = true
  error.value = null
  showLoadingToast()

  try {
    // 从store获取地址列表
    await userStore.queryAddrList({ force: true })
    addressList.value = userStore.addressList || []

    // 滚动到默认地址
    const defaultAddress = addressList.value.find(item => item.isDefault === '1')
    if (defaultAddress?.addressId) {
      // 使用 nextTick 确保DOM更新完成
      await nextTick()
      scrollToAddress(defaultAddress.addressId)
    }
  } catch (err) {
    console.error('获取地址列表失败:', err)
    error.value = err
    showToast(err.msg || '获取地址列表失败')
    addressList.value = []
  } finally {
    loading.value = false
    closeToast()
  }
}

// ==================== DOM操作方法 ====================
// 滚动到指定地址
const scrollToAddress = debounce((addressId) => {
  if (!addressId || !addressListRef.value) return

  const selectedElement = addressItemRefs.value.get(addressId)
  if (selectedElement) {
    selectedElement.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
      inline: 'nearest'
    })
  }
}, 100)

// ==================== 事件处理方法 ====================
// 新增地址
const handleAddAddress = () => {
  router.push('/addr/add')
}

// 编辑地址
const handleEditAddress = (address) => {
  router.push({
    path: '/addr/edit',
    query: { addrId: address.addressId, isEdit: 1 },
    params: { address }
  })
}

// 删除地址
const handleDeleteAddress = debounce(async (address) => {
  try {
    await $alert({
      title: '删除地址',
      message: '确定要删除该地址吗？',
      showCancelButton: true,
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      onConfirmCallback: async () => {
        showLoadingToast()
        try {
          const [err] = await deleteAddr(address.addressId)

          if (!err) {
            // 从本地列表中移除该地址（优化：使用filter创建新数组）
            const newList = addressList.value.filter(item => item.addressId !== address.addressId)
            addressList.value = newList

            // 更新store中的地址列表数据
            userStore.setAddrList(newList)

            // 如果删除的是默认地址，需要清除当前默认地址信息
            if (address.isDefault === '1') {
              userStore.setDefaultAddr(null)
            }

            showToast('删除成功')
          } else {
            console.error('删除地址失败:', err)
            showToast(err.msg || '删除地址失败')
          }
        } catch (error) {
          console.error('删除地址异常:', error)
          showToast('删除地址失败')
        } finally {
          closeToast()
        }
      }
    })
  } catch (error) {
    console.error('删除地址弹窗异常:', error)
  }
}, 300)

// 选择地址（设置默认地址
const handleSelectAddress = debounce(async (address) => {
  if (loading.value) return // 防止重复操作
  
  // 如果当前地址已经是默认地址，则不需要调用接口
  if (address.isDefault === '1') {
    return
  }

  showLoadingToast()

  try {
    const [err] = await updateUserDefaultAddr(address.addressId)

    if (!err) {
      const newList = addressList.value.map(item => ({
        ...item,
        isDefault: item.addressId === address.addressId ? '1' : '0'
      }))

      addressList.value = newList

      // 更新store中的地址列表数据
      userStore.setAddrList(newList)

      // 更新store中的当前默认地址数据
      userStore.setDefaultAddr({ ...address, isDefault: '1' })

      showToast('设置默认地址成功')
    } else {
      console.error('设置默认地址失败:', err)
      showToast(err.msg || '设置默认地址失败')
    }
  } catch (error) {
    console.error('设置默认地址异常:', error)
    showToast('设置默认地址失败')
  } finally {
    closeToast()
  }
}, 300)

// ==================== 生命周期钩子 ====================
onMounted(() => {
  loadAddressList()
})

onUnmounted(() => {
  scrollToAddress.cancel()
})

// ==================== 组件暴露 ====================
// 暴露方法供外部调用
defineExpose({
  scrollToAddress,
  loadAddressList,
  addressList: readonly(addressList)
})
</script>

<style scoped lang="less">
.address-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: @bg-color-gray;
  overflow: hidden;
  box-sizing: border-box;

  &--has-data {
    padding-bottom: 65px;
  }

  &__container {
    flex: 1;
    padding: 10px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  &__list {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch; // iOS 滚动优化
  }

  &__item {
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__empty {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100;
  }
}

// 骨架屏样式
.address-skeleton {
  padding: 15px;
  background-color: @bg-color-white;
  border-radius: @radius-4;
  margin-bottom: 8px;

  &__content {
    margin-bottom: 16px;
  }

  &__header {
    margin-bottom: 11px;
  }

  &__user-info {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}

.skeleton-line {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-name {
  width: 60px;
  height: 16px;
}

.skeleton-phone {
  width: 100px;
  height: 16px;
}

.skeleton-address {
  width: 100%;
  height: 13px;
  margin-bottom: 6px;
}

.skeleton-address-detail {
  width: 80%;
  height: 13px;
}

.skeleton-btn {
  width: 40px;
  height: 25px;
  border-radius: @radius-4;
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}
</style>
